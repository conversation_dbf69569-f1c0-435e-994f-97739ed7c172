@startuml
!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor lightblue
    BorderColor black
}
skinparam actor {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam maxMessageSize 150

title Firebase Token Management Sequence

actor "User" as user
participant "Client App\n(iOS/Web)" as client
participant "Firebase SDK" as firebase
participant "FCM Service" as fcm
participant "Backend API" as backend
participant "Database" as db

== Token Initialization ==
user -> client : Launch application
client -> firebase : Initialize Firebase SDK
firebase -> firebase : Request permissions

alt Permission Granted
    firebase -> fcm : Request FCM token
    fcm -> firebase : Return token
    firebase -> client : Token ready

    client -> backend : PUT /notifications/fcm-token\n{userId, fcmToken, platform}
    backend -> db : Save FCM token
    db -> backend : Token saved
    backend -> client : 200 OK
    client -> user : App ready for notifications

else Permission Denied
    firebase -> client : Permission denied
    client -> user : Notifications disabled
end

== Token Refresh ==
note over firebase : Tokens refresh periodically
firebase -> firebase : onTokenRefresh triggered
firebase -> fcm : Request new token
fcm -> firebase : Return new token
firebase -> client : New token available

client -> backend : PUT /notifications/fcm-token\n{userId, newToken}
backend -> db : Update FCM token
db -> backend : Token updated
backend -> client : 200 OK

== Token Validation ==
note over backend : Validate before sending notifications
backend -> fcm : Validate token
alt Token Valid
    fcm -> backend : Valid
    backend -> backend : Send notification
else Token Invalid
    fcm -> backend : Invalid/expired
    backend -> db : Mark token invalid
    backend -> backend : Skip notification
end

== User Logout ==
user -> client : Logout
client -> backend : POST /auth/logout
backend -> db : Clear FCM token
db -> backend : Token cleared
backend -> client : 200 OK
client -> firebase : Clear local token
firebase -> client : Token cleared

@enduml
