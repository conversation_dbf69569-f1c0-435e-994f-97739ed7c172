@startuml sprint3_class_diagram
!theme plain
skinparam classAttributeIconSize 0
skinparam classFontSize 8
skinparam packageFontSize 10
skinparam maxMessageSize 80
skinparam wrapWidth 150
skinparam minClassWidth 100
skinparam linetype ortho

' Core Domain Entities
class Order {
    +id: string
    +patientId: string
    +status: OrderStatus
    +orderDate: Date
    +totalAmount: number
    +productIds: string[]
    +quantity: number
    +deliveryManNote?: string
}

enum OrderStatus {
    PENDING
    APPROVED
    IN_TRANSIT
    DELIVERED
    CANCELLED
}

class Product {
    +id: string
    +pharmacyId: string
    +name: string
    +description: string
    +quantity: number
    +price: number
    +stockStatus: StockStatus
    +categoryId: string
    +expiryDate: Date
    +supplier: string
    +barcode: string
    +brand: string
    +actif: boolean
}

enum StockStatus {
    IN_STOCK
    OUT_OF_STOCK
}

class Category {
    +id: string
    +name: string
}

' Repository Layer
class OrderRepository {
    +create(order): Promise<Order>
    +update(id, update): Promise<Order>
    +findById(id): Promise<Order>
    +findBasketByPatientId(patientId): Promise<Order>
    +findAllByPatientId(patientId): Promise<Order[]>
}

class ProductRepository {
    +create(product): Promise<Product>
    +update(id, update): Promise<Product>
    +findById(id): Promise<Product>
    +findAll(): Promise<Product[]>
    +findWithFilters(filters): Promise<Product[]>
}

class CategoryRepository {
    +create(category): Promise<Category>
    +findById(id): Promise<Category>
    +findAll(): Promise<Category[]>
}

' Use Cases
class OrderAddShop {
    +execute(command): Promise<Order>
}

class OrderConfirm {
    +execute(command): Promise<Order>
}

class OrderGetBasket {
    +execute(patientId): Promise<Order>
}

class ProductCreate {
    +execute(command): Promise<Product>
}

class ProductGetAll {
    +execute(command): Promise<Product[]>
}

class CategoryCreate {
    +execute(command): Promise<Category>
}

' Controllers
class OrderController {
    +addToBasket(req, body): Promise<any>
    +confirmOrder(req, body): Promise<any>
    +getBasket(req): Promise<any>
    +markOrderAsDelivered(id): Promise<any>
}

class ProductController {
    +createProduct(req, body): Promise<any>
    +getAllProducts(query): Promise<any>
    +getProductById(id): Promise<any>
}

class CategoryController {
    +createCategory(body): Promise<any>
    +getAllCategories(): Promise<any>
}

' Relationships
Order ||--|| OrderStatus
Product ||--|| StockStatus
Product }|--|| Category

' Repository relationships
OrderRepository ||--o{ Order
ProductRepository ||--o{ Product
CategoryRepository ||--o{ Category

' Controller to Use Case relationships
OrderController --> OrderAddShop
OrderController --> OrderConfirm
OrderController --> OrderGetBasket

ProductController --> ProductCreate
ProductController --> ProductGetAll

CategoryController --> CategoryCreate

' Use Case to Repository relationships
OrderAddShop --> OrderRepository
OrderConfirm --> OrderRepository
OrderConfirm --> ProductRepository
OrderGetBasket --> OrderRepository

ProductCreate --> ProductRepository
ProductGetAll --> ProductRepository

CategoryCreate --> CategoryRepository

' Domain relationships
Order }|--o{ Product : contains

@enduml
